{"projects": [{"id": 1, "title": "Portfolio Website", "description": "Personal portfolio built with Next.js and Tailwind CSS to showcase projects, experience, and writing with a performant, responsive UI.", "techStack": ["Next.Js", "Tailwind CSS", "TypeScript"], "githubLink": "https://github.com/AashishTangnami/portfolio", "externalLink": "https://aashishtangnami.vercel.app", "backgroundImage": ""}, {"id": 2, "title": "Big Data Architecture", "description": "Educational site illustrating Hadoop, Hive, and Spark architectures with clear data flows, components, and practical examples.", "techStack": ["JavaScript", "Python", "<PERSON><PERSON>", "PySpark", "Hive"], "githubLink": "https://github.com/AashishTangnami/bigdata-architecture", "externalLink": "https://bigdata-architecture.vercel.app", "backgroundImage": ""}, {"id": 3, "title": "Brain Tumor Classifier", "description": "Brain tumor classifier by fine‑tuning EfficientNet‑V2‑S in PyTorch, delivered with a Streamlit front end for interactive inference.", "techStack": ["Python", "Pytor<PERSON>", "HuggingFace", "Streamlit"], "githubLink": "https://github.com/AashishTangnami/BrainTumorDetection", "externalLink": "https://huggingface.co/spaces/AashishTangnami/<PERSON>_Tumor_Detection", "backgroundImage": ""}, {"id": 4, "title": "Data Engineering: ETL Pipeline", "description": "Production‑ready ETL on Databricks (Apache Spark, Delta Lake) to ingest, transform, and model data from data lake sources.", "techStack": ["Python", "<PERSON><PERSON>", "PySpark", "DataBricks", "Jupyter Notebook"], "githubLink": "https://github.com/AashishTangnami/Apache-Spark-Data-Engineering", "externalLink": "#", "backgroundImage": ""}, {"id": 5, "title": "Terakoya Academia Inc [ Now TAI Inc]", "description": "Internal web platform for Terakoya Academia Inc.; initially built in Django and later migrated to PHP to align with hosting and team workflows.", "techStack": ["Python", "Django", "React.Js", "PHP"], "githubLink": "#", "externalLink": "https://tai.com.np", "backgroundImage": "#"}, {"id": 6, "title": "ETL Pipeline", "description": "Containerized ETL service in Rust that ingests, validates, and loads data into PostgreSQL with a focus on reliability and horizontal scaling.", "techStack": ["Rust", "<PERSON>er", "PostgreSQL"], "githubLink": "#", "externalLink": "#", "backgroundImage": "#"}, {"id": 7, "title": "Leetcode + HackerRank Solutions", "description": "Curated solutions to 50+ SQL challenges (LeetCode/HackerRank), highlighting query patterns and best practices for data work.", "techStack": ["SQL", "Python", "PostgreSQL"], "githubLink": "https://github.com/AashishTangnami/leetcode", "externalLink": "https://leetcode.com/u/AashishTangnami/", "backgroundImage": "#"}]}