export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON><PERSON><PERSON>",
    "jobTitle": "Data Engineer",
    "description": "Data Engineer and Software Engineer with 5+ years of experience",
    "url": "https://aashishtangnami.vercel.app",
    "sameAs": [
      "https://github.com/AashishTangnami",
      "https://www.linkedin.com/in/aashishtangnami/",
      "https://leetcode.com/u/AashishTangnami/"
    ],
    "knowsAbout": [
      "Data Engineering",
      "Software Engineering", 
      "AWS",
      "Azure",
      "Python",
      "PySpark",
      "Snowflake",
      "Next.js"
    ],
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "Ontario College - AI and Data Science"
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Toronto",
      "addressCountry": "Canada"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}