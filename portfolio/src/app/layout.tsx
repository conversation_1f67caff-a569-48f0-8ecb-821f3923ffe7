import type { Metadata } from "next";
import { JetBrains_Mono } from "next/font/google";
import "./globals.css";

const jet_brain_mono = JetBrains_Mono({ 
  subsets: ["latin"],
  weight: ["100","200", "300", "400", "500", "600", "700", "800"],
  variable: "--font-jetbrainsMono",
});

export const metadata: Metadata = {
  metadataBase: new URL('https://aashishtangnami.vercel.app'),
  title: {
    default: "Aashish Tangnami - Data Engineer & Software Engineer",
    template: "%s | Aashish <PERSON>nam<PERSON>"
  },
  description: "Data Engineer and Software Engineer with 5+ years of experience in scalable ETL/ELT pipelines, cloud platforms (AWS, Azure), and full-stack development. Specialized in Python, PySpark, Snowflake, and modern web technologies.",
  keywords: [
    "Data Engineer", 
    "Software Engineer", 
    "AWS", 
    "Azure", 
    "Python", 
    "PySpark", 
    "Snowflake", 
    "Next.js", 
    "ETL", 
    "Machine Learning",
    "Toronto",
    "Canada"
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON>",
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://aashishtangnami.vercel.app',
    title: 'Aashish Tangnami - Data Engineer & Software Engineer',
    description: 'Data Engineer and Software Engineer with 5+ years of experience in scalable data pipelines and full-stack development.',
    siteName: 'Aashish Tangnami Portfolio',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Aashish Tangnami - Data Engineer & Software Engineer',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Aashish Tangnami - Data Engineer & Software Engineer',
    description: 'Data Engineer and Software Engineer with 5+ years of experience in scalable data pipelines and full-stack development.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://aashishtangnami.vercel.app',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth min-h-[75vh] mx-auto px-8 py-12 max-w-7xl">
      <body className={jet_brain_mono.variable}>
        {/* <Header/> */}
        {children}
        {/* <Footer/> */}
        </body>
    </html>
    
  );
}
