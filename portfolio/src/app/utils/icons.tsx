import React from 'react';
import {
  SiNextdotjs,
  SiTailwindcss,
  SiTypescript,
  SiJavascript,
  SiPython,
  SiApachehadoop,
  SiApachespark,
  SiApachehive,
  SiPytorch,
  SiHuggingface,
  SiStreamlit,
  SiDatabricks,
  SiJupyter,
  SiDjango,
  SiReact,
  SiRust,
  SiDocker,
  SiPostgresql,
  SiMysql,
  SiTensorflow,
  SiNumpy,
  SiFastapi,
  SiFlask,
  SiJupyter as SiJupyterAlt,
  SiSqlite,
  SiR,
  SiPandas,
  SiMetasploit,
  SiGatsby,
  SiWordpress,
  SiPrismic,
  SiNetlify,
  SiElasticsearch,
  SiSonarqube,
    SiAmazon,
    SiAwslambda,
    SiAmazonredshift,
    SiAmazons3,
  SiAmazonec2,
  SiJenkins,
  SiMongodb,
  SiCraftcms,
  SiPlotly,
  SiAngular,
  SiKubernetes,
  SiScikitlearn,
  SiPhp,
  SiSnowflake,
  SiApachekafka,
  SiApacheairflow,
  SiTerraform,
  SiGitlab,
  SiPowerbi,
  SiDbt,
  SiSplunk,
  SiMicrosoftazure,
  SiAzuredevops,
  SiConfluence,
  SiGithub,
  SiHtml5,
  SiCss3,
  SiBootstrap,
    SiVisualstudiocode,
  
} from 'react-icons/si';
import { AiOutlineConsoleSql } from "react-icons/ai";
import { FaPython, FaDocker, FaJsSquare, FaGitAlt, FaShieldAlt, FaJava } from 'react-icons/fa';

// Normalize tech labels for consistent lookups
export const normalizeTech = (label: string): string =>
  label
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // remove non-alphanumerics
    .replace(/^pyspark$/g, 'pyspark')
    .replace(/^nextjs$/g, 'nextjs');

// Project: brand-colored icons
export const PROJECT_BRANDED_ICONS: Record<string, (className?: string) => JSX.Element> = {
  nextjs: (cls = 'w-4 h-4') => <SiNextdotjs className={`${cls} text-[#000000]`} />,
  tailwindcss: (cls = 'w-4 h-4') => <SiTailwindcss className={`${cls} text-[#38BDF8]`} />,
  typescript: (cls = 'w-4 h-4') => <SiTypescript className={`${cls} text-[#3178C6]`} />,
  javascript: (cls = 'w-4 h-4') => <SiJavascript className={`${cls} text-[#F7DF1E]`} />,
  python: (cls = 'w-4 h-4') => <SiPython className={`${cls} text-[#3776AB]`} />,
  hadoop: (cls = 'w-4 h-4') => <SiApachehadoop className={`${cls} text-[#FFCC00]`} />,
  spark: (cls = 'w-4 h-4') => <SiApachespark className={`${cls} text-[#E25A1C]`} />,
  pyspark: (cls = 'w-4 h-4') => <SiApachespark className={`${cls} text-[#E25A1C]`} />,
  hive: (cls = 'w-4 h-4') => <SiApachehive className={`${cls} text-[#FDEE21]`} />,
  pytorch: (cls = 'w-4 h-4') => <SiPytorch className={`${cls} text-[#EE4C2C]`} />,
  huggingface: (cls = 'w-4 h-4') => <SiHuggingface className={`${cls} text-[#FFCA28]`} />,
  streamlit: (cls = 'w-4 h-4') => <SiStreamlit className={`${cls} text-[#FF4B4B]`} />,
  databricks: (cls = 'w-4 h-4') => <SiDatabricks className={`${cls} text-[#FF3621]`} />,
  jupyternotebook: (cls = 'w-4 h-4') => <SiJupyter className={`${cls} text-[#F37626]`} />,
  django: (cls = 'w-4 h-4') => <SiDjango className={`${cls} text-[#092E20]`} />,
  reactjs: (cls = 'w-4 h-4') => <SiReact className={`${cls} text-[#61DAFB]`} />,
  rust: (cls = 'w-4 h-4') => <SiRust className={`${cls} text-[#000000]`} />,
  docker: (cls = 'w-4 h-4') => <SiDocker className={`${cls} text-[#2496ED]`} />,
  pandas: (cls = 'w-4 h-4') => <SiPandas className={`${cls} text-[#2496ED]`} />,
  kubernetes: (cls = 'w-4 h-4') => <SiKubernetes className={`${cls} text-[#326CE5]`} />,
  scikitlearn: (cls = 'w-4 h-4') => <SiScikitlearn className={`${cls} text-[#F7931E]`} />,
  snowflake: (cls = 'w-4 h-4') => <SiSnowflake className={`${cls} text-[#29B5E8]`} />,
  apachekafka: (cls = 'w-4 h-4') => <SiApachekafka className={`${cls} text-[#231F20]`} />,
  apacheairflow: (cls = 'w-4 h-4') => <SiApacheairflow className={`${cls} text-[#017CEE]`} />,
  terraform: (cls = 'w-4 h-4') => <SiTerraform className={`${cls} text-[#844FBA]`} />,
  gitlab: (cls = 'w-4 h-4') => <SiGitlab className={`${cls} text-[#FC6D26]`} />,
  powerbi: (cls = 'w-4 h-4') => <SiPowerbi className={`${cls} text-[#F2C811]`} />,
  dbt: (cls = 'w-4 h-4') => <SiDbt className={`${cls} text-[#FF694B]`} />,
  splunk: (cls = 'w-4 h-4') => <SiSplunk className={`${cls} text-[#000000]`} />,
  postgresql: (cls = 'w-4 h-4') => <SiPostgresql className={`${cls} text-[#4169E1]`} />,
  sql: (cls = 'w-4 h-4') => <AiOutlineConsoleSql className={`${cls} text-[#4479A1]`} />,
  php: (cls = 'w-4 h-4') => <SiPhp className={`${cls} text-[#777BB4]`} />,
  // synonyms and additional services
  s3: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  glue: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  lambda: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  redshift: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  stepfunctions: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  awsemr: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  kinesis: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  athena: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  iam: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  lakeformation: (cls = 'w-4 h-4') => <SiAmazon className={`${cls} text-[#FF9900]`} />,
  azuredatabricks: (cls = 'w-4 h-4') => <SiMicrosoftazure className={`${cls} text-[#0078D4]`} />,
  azuredatafactory: (cls = 'w-4 h-4') => <SiMicrosoftazure className={`${cls} text-[#0078D4]`} />,
  azuredatalakegen2: (cls = 'w-4 h-4') => <SiMicrosoftazure className={`${cls} text-[#0078D4]`} />,
  azuredevops: (cls = 'w-4 h-4') => <SiAzuredevops className={`${cls} text-[#0078D7]`} />,
  azureblobstorage: (cls = 'w-4 h-4') => <SiMicrosoftazure className={`${cls} text-[#0078D4]`} />,
  azurekeyvault: (cls = 'w-4 h-4') => <SiMicrosoftazure className={`${cls} text-[#0078D4]`} />,
  confluence: (cls = 'w-4 h-4') => <SiConfluence className={`${cls} text-[#172B4D]`} />,
  github: (cls = 'w-4 h-4') => <SiGithub className={`${cls} text-[#181717]`} />,
  vscode: (cls = 'w-4 h-4') => <SiVisualstudiocode className={`${cls} text-[#007ACC]`} />,
  html5: (cls = 'w-4 h-4') => <SiHtml5 className={`${cls} text-[#E34F26]`} />,
  css3: (cls = 'w-4 h-4') => <SiCss3 className={`${cls} text-[#1572B6]`} />,
  bootstrap: (cls = 'w-4 h-4') => <SiBootstrap className={`${cls} text-[#7952B3]`} />,
  angularjs: (cls = 'w-4 h-4') => <SiAngular className={`${cls} text-[#DD0031]`} />,
  java: (cls = 'w-4 h-4') => <FaJava className={`${cls} text-[#007396]`} />,
};

export const getProjectIcon = (label: string, sizeClass = 'w-4 h-4'): JSX.Element | null => {
  const key = normalizeTech(label);
  const maker = PROJECT_BRANDED_ICONS[key];
  return maker ? maker(sizeClass) : null;
};

// Experience/About registries
export const ABOUT_ICON_COMPONENTS = {
  // Row 1
  python: FaPython,
  javascript: FaJsSquare,
  typescript: SiTypescript,
  rust: SiRust,
  r: SiR,
  nextdotjs: SiNextdotjs,
  fastapi: SiFastapi,
  flask: SiFlask,
  php: SiPhp,
  // Row 2
  pytorch: SiPytorch,
  tensorflow: SiTensorflow,
  scikitlearn: SiScikitlearn,
  pandas: SiPandas,
  numpy: SiNumpy,
  jupyter: SiJupyterAlt,
  plotly: SiPlotly,
  matplotlib: SiMetasploit,
  // Row 3
  docker: FaDocker,
  kubernetes: SiKubernetes,
  git: FaGitAlt,
  mongodb: SiMongodb,
  sqlite: SiSqlite,
  // Cloud/data extras
  awsglue: SiAmazon,
  amazons3: SiAmazons3,
  awslambda: SiAwslambda,
  awsstepfunctions: SiAmazon,
  amazonredshift: SiAmazonredshift,
  snowflake: SiSnowflake,
  databricks: SiDatabricks,
  pyspark: SiApachespark,
  deltalake: SiDatabricks,
  kafka: SiApachekafka,
  airflow: SiApacheairflow,
  sql: AiOutlineConsoleSql,
  dbt: SiDbt,
  terraform: SiTerraform,
  gitlabcicd: SiGitlab,
  greatexpectations: SiPython,
  powerbi: SiPowerbi,
  awsdms: SiAmazon,
  bedrock: SiAmazon,
  mllib: SiPytorch,
  splunk: SiSplunk,
  cloudwatch: SiAmazon,
  amundsen: SiPython,
  agile: FaGitAlt,
  pcidss: FaShieldAlt,
  sox: FaShieldAlt,
  gdpr: FaShieldAlt,
  // UI/platform extras
  react: SiReact,
  gatsby: SiGatsby,
  wordpress: SiWordpress,
  prismic: SiPrismic,
  netlify: SiNetlify,
  elasticsearch: SiElasticsearch,
  sonarqube: SiSonarqube,
  amazon: SiAmazon,
  amazonec2: SiAmazonec2,
  jenkins: SiJenkins,
  django: SiDjango,
  mysql: SiMysql,
  postgresql: SiPostgresql,
  craftcms: SiCraftcms,
  angular: SiAngular,
  // Additional services/synonyms
  s3: SiAmazon,
  glue: SiAmazon,
  lambda: SiAmazon,
  redshift: SiAmazon,
  stepfunctions: SiAmazon,
  awsemr: SiAmazon,
  kinesis: SiAmazon,
  athena: SiAmazon,
  iam: SiAmazon,
  lakeformation: SiAmazon,
  azuredatabricks: SiMicrosoftazure,
  azuredatafactory: SiMicrosoftazure,
  azuredatalakegen2: SiMicrosoftazure,
  azuredevops: SiAzuredevops,
  azureblobstorage: SiMicrosoftazure,
  azurekeyvault: SiMicrosoftazure,
  confluence: SiConfluence,
  github: SiGithub,
  html5: SiHtml5,
  css3: SiCss3,
  bootstrap: SiBootstrap,
  vscode: SiVisualstudiocode,
  angularjs: SiAngular,
  java: FaJava,
};

// Grouped collections for About section usage (non-breaking)
export const ABOUT_GROUPS = {
  programmingLanguages: [
    { name: 'Python', icon: ABOUT_ICON_COMPONENTS.python },
    { name: 'JavaScript', icon: ABOUT_ICON_COMPONENTS.javascript },
    { name: 'TypeScript', icon: ABOUT_ICON_COMPONENTS.typescript },
    { name: 'Rust', icon: ABOUT_ICON_COMPONENTS.rust },
    { name: 'R', icon: ABOUT_ICON_COMPONENTS.r },
    { name: 'SQL', icon: ABOUT_ICON_COMPONENTS.sql },
    { name: 'Java', icon: ABOUT_ICON_COMPONENTS.java },
    { name: 'PHP', icon: ABOUT_ICON_COMPONENTS.php },
  ] as { name: string; icon: React.ElementType }[],

  cloudComputing: [
    { name: 'AWS Glue', icon: ABOUT_ICON_COMPONENTS.awsglue },
    { name: 'Amazon S3', icon: ABOUT_ICON_COMPONENTS.amazons3 },
    { name: 'AWS Lambda', icon: ABOUT_ICON_COMPONENTS.awslambda },
    { name: 'AWS Step Functions', icon: ABOUT_ICON_COMPONENTS.awsstepfunctions },
    { name: 'Amazon Redshift', icon: ABOUT_ICON_COMPONENTS.amazonredshift },
    { name: 'AWS EMR', icon: ABOUT_ICON_COMPONENTS.awsemr },
    { name: 'Kinesis', icon: ABOUT_ICON_COMPONENTS.kinesis },
    { name: 'Athena', icon: ABOUT_ICON_COMPONENTS.athena },
    { name: 'IAM', icon: ABOUT_ICON_COMPONENTS.iam },
    { name: 'Lake Formation', icon: ABOUT_ICON_COMPONENTS.lakeformation },
    { name: 'CloudWatch', icon: ABOUT_ICON_COMPONENTS.cloudwatch },
    { name: 'AWS DMS', icon: ABOUT_ICON_COMPONENTS.awsdms },
    { name: 'Bedrock', icon: ABOUT_ICON_COMPONENTS.bedrock },
    { name: 'Snowflake', icon: ABOUT_ICON_COMPONENTS.snowflake },
    { name: 'Databricks', icon: ABOUT_ICON_COMPONENTS.databricks },
    { name: 'Delta Lake', icon: ABOUT_ICON_COMPONENTS.deltalake },
    { name: 'Azure Databricks', icon: ABOUT_ICON_COMPONENTS.azuredatabricks },
    { name: 'Azure Data Factory', icon: ABOUT_ICON_COMPONENTS.azuredatafactory },
    { name: 'Azure Data Lake Gen2', icon: ABOUT_ICON_COMPONENTS.azuredatalakegen2 },
    { name: 'Azure Blob Storage', icon: ABOUT_ICON_COMPONENTS.azureblobstorage },
    { name: 'Azure Key Vault', icon: ABOUT_ICON_COMPONENTS.azurekeyvault },
    { name: 'Azure DevOps', icon: ABOUT_ICON_COMPONENTS.azuredevops },
  ] as { name: string; icon: React.ElementType }[],

  webFrameworks: [
    { name: 'React', icon: ABOUT_ICON_COMPONENTS.react },
    { name: 'Next.js', icon: ABOUT_ICON_COMPONENTS.nextdotjs },
    { name: 'Django', icon: ABOUT_ICON_COMPONENTS.django },
    { name: 'FastAPI', icon: ABOUT_ICON_COMPONENTS.fastapi },
    { name: 'Flask', icon: ABOUT_ICON_COMPONENTS.flask },
    { name: 'Prismic', icon: ABOUT_ICON_COMPONENTS.prismic },
    { name: 'Netlify', icon: ABOUT_ICON_COMPONENTS.netlify },
    { name: 'Craft CMS', icon: ABOUT_ICON_COMPONENTS.craftcms },
    { name: 'Angular', icon: ABOUT_ICON_COMPONENTS.angular },
    { name: 'Elasticsearch', icon: ABOUT_ICON_COMPONENTS.elasticsearch },
    { name: 'SonarQube', icon: ABOUT_ICON_COMPONENTS.sonarqube },
    { name: 'Jenkins', icon: ABOUT_ICON_COMPONENTS.jenkins },
  ] as { name: string; icon: React.ElementType }[],

  databasesAndOrchestration: [
    // Databases
    { name: 'MySQL', icon: ABOUT_ICON_COMPONENTS.mysql },
    { name: 'PostgreSQL', icon: ABOUT_ICON_COMPONENTS.postgresql },
    { name: 'MongoDB', icon: ABOUT_ICON_COMPONENTS.mongodb },
    { name: 'SQLite', icon: ABOUT_ICON_COMPONENTS.sqlite },
    { name: 'Snowflake', icon: ABOUT_ICON_COMPONENTS.snowflake },
    { name: 'Amazon Redshift', icon: ABOUT_ICON_COMPONENTS.amazonredshift },
    // Data tooling / Orchestration
    { name: 'dbt', icon: ABOUT_ICON_COMPONENTS.dbt },
    { name: 'Great Expectations', icon: ABOUT_ICON_COMPONENTS.greatexpectations },
    { name: 'GitLab CI/CD', icon: ABOUT_ICON_COMPONENTS.gitlabcicd },
    { name: 'Amundsen', icon: ABOUT_ICON_COMPONENTS.amundsen },
    { name: 'Terraform', icon: ABOUT_ICON_COMPONENTS.terraform },
    { name: 'Kafka', icon: ABOUT_ICON_COMPONENTS.kafka },
    { name: 'Airflow', icon: ABOUT_ICON_COMPONENTS.airflow },
  ] as { name: string; icon: React.ElementType }[],

  machineLearning: [
    { name: 'PyTorch', icon: ABOUT_ICON_COMPONENTS.pytorch },
    { name: 'TensorFlow', icon: ABOUT_ICON_COMPONENTS.tensorflow },
    { name: 'HuggingFace', icon: ABOUT_ICON_COMPONENTS.pytorch },
    { name: 'Streamlit', icon: ABOUT_ICON_COMPONENTS.python },
    { name: 'MLlib', icon: ABOUT_ICON_COMPONENTS.mllib },
    { name: 'Bedrock', icon: ABOUT_ICON_COMPONENTS.bedrock },
    { name: 'Power BI', icon: ABOUT_ICON_COMPONENTS.powerbi },
  ] as { name: string; icon: React.ElementType }[],
}; 